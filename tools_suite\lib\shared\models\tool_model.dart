import 'package:equatable/equatable.dart';

enum ToolStatus { draft, published, archived }

class ToolModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final String creatorId;
  final ToolStatus status;
  final String? excelFilePath;
  final Map<String, dynamic>? uiConfig;
  final List<String> allowedUsers;
  final bool requiresAuth;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;
  final int downloadCount;
  final double? rating;

  const ToolModel({
    required this.id,
    required this.name,
    required this.description,
    required this.creatorId,
    this.status = ToolStatus.draft,
    this.excelFilePath,
    this.uiConfig,
    this.allowedUsers = const [],
    this.requiresAuth = true,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
    this.downloadCount = 0,
    this.rating,
  });

  factory ToolModel.fromJson(Map<String, dynamic> json) {
    return ToolModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      creatorId: json['creator_id'] as String,
      status: _parseStatus(json['status'] as String),
      excelFilePath: json['excel_file_path'] as String?,
      uiConfig: json['ui_config'] as Map<String, dynamic>?,
      allowedUsers: (json['allowed_users'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      requiresAuth: json['requires_auth'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      downloadCount: json['download_count'] as int? ?? 0,
      rating: json['rating'] as double?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'creator_id': creatorId,
      'status': status.name,
      'excel_file_path': excelFilePath,
      'ui_config': uiConfig,
      'allowed_users': allowedUsers,
      'requires_auth': requiresAuth,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata,
      'download_count': downloadCount,
      'rating': rating,
    };
  }

  static ToolStatus _parseStatus(String statusString) {
    switch (statusString.toLowerCase()) {
      case 'draft':
        return ToolStatus.draft;
      case 'published':
        return ToolStatus.published;
      case 'archived':
        return ToolStatus.archived;
      default:
        return ToolStatus.draft;
    }
  }

  ToolModel copyWith({
    String? id,
    String? name,
    String? description,
    String? creatorId,
    ToolStatus? status,
    String? excelFilePath,
    Map<String, dynamic>? uiConfig,
    List<String>? allowedUsers,
    bool? requiresAuth,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
    int? downloadCount,
    double? rating,
  }) {
    return ToolModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      creatorId: creatorId ?? this.creatorId,
      status: status ?? this.status,
      excelFilePath: excelFilePath ?? this.excelFilePath,
      uiConfig: uiConfig ?? this.uiConfig,
      allowedUsers: allowedUsers ?? this.allowedUsers,
      requiresAuth: requiresAuth ?? this.requiresAuth,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
      downloadCount: downloadCount ?? this.downloadCount,
      rating: rating ?? this.rating,
    );
  }

  bool get isDraft => status == ToolStatus.draft;
  bool get isPublished => status == ToolStatus.published;
  bool get isArchived => status == ToolStatus.archived;

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        creatorId,
        status,
        excelFilePath,
        uiConfig,
        allowedUsers,
        requiresAuth,
        createdAt,
        updatedAt,
        metadata,
        downloadCount,
        rating,
      ];
}
