class AppConstants {
  // App Information
  static const String appName = 'Tools Suite';
  static const String appVersion = '1.0.0';
  
  // Supabase Configuration
  static const String supabaseUrl = 'YOUR_SUPABASE_URL';
  static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
  
  // User Roles
  static const String adminRole = 'admin';
  static const String userRole = 'user';
  static const String guestRole = 'guest';
  
  // Storage Keys
  static const String userRoleKey = 'user_role';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // File Extensions
  static const List<String> excelExtensions = ['.xlsx', '.xls'];
  static const List<String> supportedFileTypes = [
    'xlsx',
    'xls',
    'csv',
  ];
  
  // UI Constants
  static const double sidebarWidth = 280.0;
  static const double toolbarHeight = 56.0;
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Excel Constants
  static const int maxRows = 1048576;
  static const int maxColumns = 16384;
  static const String defaultSheetName = 'Sheet1';
  
  // UI Builder Constants
  static const List<String> componentTypes = [
    'input_field',
    'output_display',
    'button',
    'label',
    'dropdown',
    'checkbox',
    'radio',
    'slider',
    'date_picker',
    'color_picker',
  ];
  
  // Database Tables
  static const String usersTable = 'users';
  static const String toolsTable = 'tools';
  static const String permissionsTable = 'permissions';
  static const String storeTable = 'store';
  
  // Error Messages
  static const String networkError = 'Network connection error';
  static const String authError = 'Authentication failed';
  static const String permissionError = 'Insufficient permissions';
  static const String fileError = 'File operation failed';
}
