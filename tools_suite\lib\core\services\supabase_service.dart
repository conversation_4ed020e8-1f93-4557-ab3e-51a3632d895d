import 'dart:typed_data';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../constants/app_constants.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();
  
  SupabaseService._();
  
  SupabaseClient get client => Supabase.instance.client;
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: AppConstants.supabaseUrl,
      anonKey: AppConstants.supabaseAnonKey,
    );
  }
  
  // Auth methods
  User? get currentUser => client.auth.currentUser;
  
  Future<AuthResponse> signInWithEmail(String email, String password) async {
    return await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }
  
  Future<AuthResponse> signUpWithEmail(String email, String password) async {
    return await client.auth.signUp(
      email: email,
      password: password,
    );
  }
  
  Future<void> signOut() async {
    await client.auth.signOut();
  }
  
  // Database methods
  Future<List<Map<String, dynamic>>> getUsers() async {
    final response = await client
        .from(AppConstants.usersTable)
        .select();
    return response;
  }
  
  Future<Map<String, dynamic>?> getUserById(String userId) async {
    final response = await client
        .from(AppConstants.usersTable)
        .select()
        .eq('id', userId)
        .maybeSingle();
    return response;
  }
  
  Future<void> createUser(Map<String, dynamic> userData) async {
    await client
        .from(AppConstants.usersTable)
        .insert(userData);
  }
  
  Future<void> updateUser(String userId, Map<String, dynamic> updates) async {
    await client
        .from(AppConstants.usersTable)
        .update(updates)
        .eq('id', userId);
  }
  
  Future<void> deleteUser(String userId) async {
    await client
        .from(AppConstants.usersTable)
        .delete()
        .eq('id', userId);
  }
  
  // Tools methods
  Future<List<Map<String, dynamic>>> getTools() async {
    final response = await client
        .from(AppConstants.toolsTable)
        .select();
    return response;
  }
  
  Future<Map<String, dynamic>?> getToolById(String toolId) async {
    final response = await client
        .from(AppConstants.toolsTable)
        .select()
        .eq('id', toolId)
        .maybeSingle();
    return response;
  }
  
  Future<void> createTool(Map<String, dynamic> toolData) async {
    await client
        .from(AppConstants.toolsTable)
        .insert(toolData);
  }
  
  Future<void> updateTool(String toolId, Map<String, dynamic> updates) async {
    await client
        .from(AppConstants.toolsTable)
        .update(updates)
        .eq('id', toolId);
  }
  
  Future<void> deleteTool(String toolId) async {
    await client
        .from(AppConstants.toolsTable)
        .delete()
        .eq('id', toolId);
  }
  
  // Storage methods
  Future<String> uploadFile(String bucket, String path, List<int> fileBytes) async {
    await client.storage
        .from(bucket)
        .uploadBinary(path, Uint8List.fromList(fileBytes));
    
    return client.storage
        .from(bucket)
        .getPublicUrl(path);
  }
  
  Future<List<int>> downloadFile(String bucket, String path) async {
    return await client.storage
        .from(bucket)
        .download(path);
  }
  
  Future<void> deleteFile(String bucket, String path) async {
    await client.storage
        .from(bucket)
        .remove([path]);
  }
}
